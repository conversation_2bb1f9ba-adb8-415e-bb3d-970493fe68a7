import Box from '@mui/joy/Box';
import React from 'react';
import { FileType, isWhatFileType, renderFileIconUrl } from '@bika/ui/file';
import { useUIFrameworkContext } from '@bika/ui/framework';

interface AttachmentData {
  name?: string;
  contentType?: string;
  url: string;
}

interface AttachmentRendererProps {
  attachment: AttachmentData;
  width?: number | string;
  height?: number | string;
  borderRadius?: number | string;
  marginBottom?: number | string;
  alignSelf?: 'flex-start' | 'flex-end' | 'center' | 'stretch';
  onClick?: (attachment: AttachmentData) => void;
  onHover?: (attachment: AttachmentData) => void;
  className?: string;
}

// Helper function to get attachment display path for experimental_attachments
const getAttachmentDisplayPath = (attachment: AttachmentData) => {
  const fileName = attachment.name || 'attachment';
  const mimeType = attachment.contentType || 'application/octet-stream';

  // Determine file type using the utility function
  const fileType = isWhatFileType({ name: fileName, type: mimeType });

  switch (fileType) {
    case FileType.Image: {
      // For images, remove query parameters from URL for cleaner display
      const urlIndex = attachment.url.indexOf('?');
      return urlIndex === -1 ? attachment.url : attachment.url.slice(0, urlIndex);
    }
    default: {
      // For non-images, use the appropriate file type icon
      return renderFileIconUrl({ name: fileName, type: mimeType });
    }
  }
};

export function ChatAttachmentRenderer({
  attachment,
  width = 64,
  height = 64,
  borderRadius = 4,
  marginBottom = 1,
  alignSelf = 'flex-end',
  onClick,
  onHover,
  className,
}: AttachmentRendererProps) {
  const { Image } = useUIFrameworkContext();
  const fileName = attachment.name || 'attachment';
  const mimeType = attachment.contentType || 'application/octet-stream';
  const fileType = isWhatFileType({ name: fileName, type: mimeType });
  const displayPath = getAttachmentDisplayPath(attachment);

  const handleClick = () => {
    if (onClick) {
      onClick(attachment);
    }
  };

  const handleMouseEnter = () => {
    if (onHover) {
      onHover(attachment);
    }
  };

  const renderContent = () => {
    switch (fileType) {
      case FileType.Image:
        return (
          <Image
            src={displayPath}
            alt={fileName}
            width={typeof width === 'number' ? width : undefined}
            height={typeof height === 'number' ? height : undefined}
            style={{
              width: typeof width === 'string' ? width : `${width}px`,
              height: typeof height === 'string' ? height : `${height}px`,
              borderRadius: typeof borderRadius === 'string' ? borderRadius : `${borderRadius}px`,
              objectFit: 'cover',
              cursor: onClick ? 'pointer' : 'default',
            }}
          />
        );

      default:
        // For PDFs and other file types, show file icon with filename
        return (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: typeof width === 'string' ? width : `${width}px`,
              height: typeof height === 'string' ? height : `${height}px`,
              borderRadius: typeof borderRadius === 'string' ? borderRadius : `${borderRadius}px`,
              backgroundColor: 'var(--bg-controls)',
              border: '1px solid var(--border-default)',
              cursor: onClick ? 'pointer' : 'default',
              '&:hover': onClick
                ? {
                    backgroundColor: 'var(--bg-controls-hover)',
                  }
                : {},
            }}
          >
            <Image
              src={displayPath}
              alt={fileName}
              width={24}
              height={24}
              style={{
                objectFit: 'contain',
              }}
            />
            <Box
              sx={{
                fontSize: '10px',
                color: 'var(--text-secondary)',
                textAlign: 'center',
                marginTop: '4px',
                maxWidth: '100%',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                padding: '0 4px',
              }}
            >
              {fileName}
            </Box>
          </Box>
        );
    }
  };

  return (
    <Box
      className={className}
      sx={{
        mb: marginBottom,
        alignSelf,
        transition: 'transform 0.2s ease-in-out',
        '&:hover': onClick
          ? {
              transform: 'scale(1.02)',
            }
          : {},
      }}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
    >
      {renderContent()}
    </Box>
  );
}
