import { create } from 'zustand';
import type { PreviewAttachment } from './utils/attachment-preview';

interface InputStore {
  attachments: PreviewAttachment[];
  setAttachments: (attachments: PreviewAttachment[] | ((prev: PreviewAttachment[]) => PreviewAttachment[])) => void;
  clearAttachments: () => void;
}

export const useInputStore = create<InputStore>((set, get) => ({
  attachments: [],
  setAttachments: (attachments) => {
    if (typeof attachments === 'function') {
      set({ attachments: attachments(get().attachments) });
    } else {
      set({ attachments });
    }
  },
  clearAttachments: () => set({ attachments: [] }),
}));
